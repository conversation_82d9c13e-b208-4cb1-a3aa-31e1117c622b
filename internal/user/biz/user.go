package biz

import (
	"context"
	"errors"

	"github.com/go-kratos/kratos/v2/log"
)

// UserRepo 与数据库交互的接口
type UserRepo interface {
	CreateUser(*RegisterUser) error
}

type RegisterUser struct {
	UserName   string
	Password   string
	Email      string
	RePassword string
}

type UserUsecase struct {
	repo UserRepo
	log  *log.Helper
}

func NewRegisterUser(userName, password, email, rePassword string) *RegisterUser {
	return &RegisterUser{UserName: userName, Password: password, Email: email, RePassword: rePassword}
}

func NewUserUsecase(repo UserRepo, logger log.Logger) *UserUsecase {
	return &UserUsecase{repo: repo, log: log.NewHelper(logger)}
}

func (uc *UserUsecase) RegisterUser(ctx context.Context, ru *RegisterUser) error {
	if ru.Password != ru.RePassword {
		uc.log.Errorf("RegisterUser: Password : %v != RePassword : %v", ru.Password, ru.RePassword)
		return errors.New("密码不一致")
	}
	uc.log.Infof("RegisterUser: %v", ru)
	
	uc.repo.CreateUser(ru) // 创建用户
	return nil	
}
