package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type registerUser struct{
	UserName string
	Password string
	Email string
}

func NewRegisterUser(userName, password, email string) *registerUser {
	return &registerUser{UserName: userName, Password: password, Email: email}
}

type UserUsecase struct {
	// repo
	log *log.Helper
}

func NewUserUsecase(logger log.Logger) *UserUsecase {
	return &UserUsecase{log:log.NewHelper(logger)}
}

func (uc *UserUsecase) RegisterUser(ctx context.Context,ru *registerUser) error{
	uc.log.Infof("RegisterUser: %v", ru)
	return nil
} 