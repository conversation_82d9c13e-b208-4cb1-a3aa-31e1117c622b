package server

import (
	"context"
	userV1 "kratos-community/api/user/v1"
	"kratos-community/internal/conf"
	"kratos-community/internal/user/service"

	"github.com/go-kratos/kratos/v2/log"
	jwt "github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/selector"
	"github.com/go-kratos/kratos/v2/transport/http"
	jwtv4 "github.com/golang-jwt/jwt/v4"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, user *service.UserService, auth *conf.Auth, logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			validate.Validator(),
		),
	}

	// 添加 jwt 中间件
	opts = append(opts, http.Middleware(
		selector.Server(
			// 创建 JWT 中间件
			jwt.Server(func(token *jwtv4.Token) (interface{}, error) {
				return []byte(j.Secret), nil // 使用 Nacos 中配置的 secret
			}),
		).Match(func (ctx context.Context, operation string) bool {
			// --- 定义哪些接口需要认证 ---
			// 如果 operation 是 "/api.user.v1.UserService/CreateUser"，则放行
			if operation == "/api.user.v1.UserService/CreateUser" {
				return false
			}
			// 其他所有接口都需要认证
			return true
		}).Build(),
	))
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	userV1.RegisterUserHTTPServer(srv, user)
	return srv
}
