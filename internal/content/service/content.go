package service

import (
	"context"

	pb "kratos-community/api/content/v1"
	"kratos-community/internal/content/biz"
)

type ContentService struct {
	pb.UnimplementedContentServer

	uc *biz.ContentUsecase
}

func NewContentService(uc *biz.ContentUsecase) *ContentService {
	return &ContentService{uc: uc}
}

func (s *ContentService) CreateArticle(ctx context.Context, req *pb.CreateArticleRequest) (*pb.CreateArticleReply, error) {
	article,err:=s.uc.CreArticle(ctx,req.Title,req.Content)
	if err!=nil{
		return nil,err
	}
	return &pb.CreateArticleReply{
		Article: &pb.Article{
			Id:article.Id,
			Title:article.Title,
			Content: article.Content,
			AuthorId: article.AuthorId,
			CreatedAt: article.CreatedAt,
			UpdatedAt: article.UpdatedAt,
		},
	}, nil
}
