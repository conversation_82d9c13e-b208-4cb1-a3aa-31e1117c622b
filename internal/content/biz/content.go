package biz

import (
	"context"
	"kratos-community/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
)

// ContentRepo 与数据库交互的接口
type ContentRepo interface {
	CreateArtical(ctx context.Context) error
}

type ContentUsecase struct {
	repo      ContentRepo
	log       *log.Helper
	jwtSecret string
}

func NewContentUsecase(repo ContentRepo, logger log.Logger, jwtSecret *conf.Auth) *ContentUsecase {
	return &ContentUsecase{repo: repo, log: log.NewHelper(logger), jwtSecret: jwtSecret.JwtSecret}
}
