package biz

import (
	"context"

	"kratos-community/internal/conf"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/golang-jwt/jwt/v5"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// 定义错误
var (
	ErrInternalServer = errors.InternalServer("Err_Internal_Server", "服务器出错")
)

// ContentRepo 与数据库交互的接口
type ContentRepo interface {
	CreateArtical(ctx context.Context, userid uint64, title, content string) (*Article, error)
}

type ContentUsecase struct {
	repo      ContentRepo
	log       *log.Helper
	jwtSecret string
}

type Article struct {
	Id        uint64
	Title     string
	Content   string
	AuthorId  uint64
	CreatedAt *timestamppb.Timestamp
	UpdatedAt *timestamppb.Timestamp
}

func NewContentUsecase(repo ContentRepo, logger log.Logger, jwtSecret *conf.Auth) *ContentUsecase {
	return &ContentUsecase{repo: repo, log: log.<PERSON>Helper(logger), jwtSecret: jwtSecret.JwtSecret}
}

func (uc *ContentUsecase) CreArticle(ctx context.Context, title, content string) (*Article, error) {
	// 1.往数据库插入数据
	//   通过jwt，拿到user_id，然后再插入数据
	token,ok:=jwt.FromContext(ctx)
	if !ok{
		return nil,ErrInternalServer
	}
	token.Cliams.(jwt.MapClaims)
	
	// 2.获取数据

	// 3.返回结果
	return nil, nil
}
