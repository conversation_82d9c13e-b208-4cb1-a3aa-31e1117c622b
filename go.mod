module kratos-community

go 1.21

toolchain go1.22.6

require (
	github.com/envoyproxy/protoc-gen-validate v1.0.4
	github.com/go-kratos/kratos/v2 v2.8.0
	github.com/google/wire v0.6.0
	go.uber.org/automaxprocs v1.5.1
	google.golang.org/genproto/googleapis/api v0.0.0-20240528184218-************
	google.golang.org/grpc v1.65.0
	google.golang.org/protobuf v1.34.1
	gorm.io/driver/mysql v1.6.0
	gorm.io/gorm v1.30.1
)

require (
	dario.cat/mergo v1.0.0 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/go-kratos/aegis v0.2.0 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/kr/text v0.2.0 // indirect
	go.opentelemetry.io/otel v1.24.0 // indirect
	go.opentelemetry.io/otel/metric v1.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.24.0 // indirect
	golang.org/x/net v0.25.0 // indirect
	golang.org/x/sync v0.9.0 // indirect
	golang.org/x/sys v0.20.0 // indirect
	golang.org/x/text v0.20.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240528184218-************ // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
