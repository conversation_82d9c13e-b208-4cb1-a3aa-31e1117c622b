// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"kratos-community/internal/conf"
	"kratos-community/internal/content/biz"
	"kratos-community/internal/content/data"
	"kratos-community/internal/content/service"
	"kratos-community/internal/server"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, auth *conf.Auth, logger log.Logger) (*kratos.App, func(), error) {
	grpcServer := server.NewGRPCServer(confServer, auth, logger)
	httpServer := server.NewHTTPServer(confServer, auth, logger)
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	contentRepo := data.NewContentRepo(dataData, logger)
	contentUsecase := biz.NewContentUsecase(contentRepo, logger, auth)
	contentService := service.NewContentService(contentUsecase)
	app := newApp(logger, grpcServer, httpServer, contentService)
	return app, func() {
		cleanup()
	}, nil
}
