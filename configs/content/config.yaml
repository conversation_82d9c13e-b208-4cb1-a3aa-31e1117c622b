server:
  http:
    addr: 0.0.0.0:8001
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9001
    timeout: 1s
data:
  # database:
  #   driver: mysql
  #   source: root:root@tcp(127.0.0.1:3306)/test?parseTime=True&loc=Local
  databases:
    user_1:
      driver: mysql
      source: user2:112233@tcp(192.168.192.1:3311)/kratos_community_content?charset=utf8mb4&parseTime=True&loc=Local
  redis:
    addr: 127.0.0.1:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
auth:
  jwt_secret: ASDFLJKNG@IR!W4RE5@6%T63&#M84*$p%4G835YGZY
