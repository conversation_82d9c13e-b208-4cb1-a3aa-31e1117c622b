# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: User API
    description: 定义 User 服务
    version: 0.0.1
paths:
    /v1/auth/login:
        post:
            tags:
                - User
            description: 用户登录
            operationId: User_Login
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/LoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/LoginReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /v1/auth/register:
        post:
            tags:
                - User
            description: 用户注册
            operationId: User_RegisterUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/RegisterUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/RegisterUserReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        LoginReply:
            type: object
            properties:
                token:
                    type: string
                userInfo:
                    $ref: '#/components/schemas/UserInfo'
        LoginRequest:
            type: object
            properties:
                userName:
                    type: string
                password:
                    type: string
        RegisterUserReply:
            type: object
            properties: {}
        RegisterUserRequest:
            type: object
            properties:
                userName:
                    type: string
                password:
                    type: string
                email:
                    type: string
            description: 用户注册请求结构体
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UserInfo:
            type: object
            properties:
                id:
                    type: string
                userName:
                    type: string
                email:
                    type: string
            description: 用户信息结构体
tags:
    - name: User
